package com.cheng.log.config;

import com.cheng.log.aop.OperateLogAspect;
import com.cheng.log.service.ErrorLogService;
import com.cheng.log.service.OperationLogService;
import com.cheng.log.service.impl.DefaultErrorLogService;
import com.cheng.log.service.impl.DefaultOperationLogServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
// 配置文件operation-log:enable = true（默认不配置就是true）,false时此配置将失效
@ConditionalOnProperty(prefix = "operation-log", value = "enable", havingValue = "true", matchIfMissing = true)
@Slf4j
public class OperationLogConfiguration {

    /**
     * 外界没有 OperationLogService 的实现时，用默认的
     */
    @Bean
    @ConditionalOnMissingBean
    public OperationLogService getOperationLogService() {
        log.info("使用默认的日志实现类,日志将输出到日志文件中...........");
        return new DefaultOperationLogServiceImpl();
    }

    @Bean
    @ConditionalOnMissingBean
    public ErrorLogService getApiErrorLogService() {
        log.info("使用默认的错误日志实现类,日志将输出到日志文件中...........");
        return new DefaultErrorLogService();
    }

    @Bean
    public OperateLogAspect operateLogAspect() {
        log.info("实例化 OperateLogAspect ...");
        return new OperateLogAspect();
    }
}
