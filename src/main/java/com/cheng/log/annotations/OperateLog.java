package com.cheng.log.annotations;

import com.cheng.log.enums.OperateTypeEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.lang.annotation.*;

/**
 * 操作日志注解
 *
 * <AUTHOR>
 */
/*@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)*/
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface OperateLog {

    // ========== 模块字段 ==========

    /**
     * 操作模块
     * <p>
     * 为空时，会尝试读取 {@link Api#value()} 属性
     */
    String module() default "";

    /**
     * 操作名
     * <p>
     * 为空时，会尝试读取 {@link ApiOperation#value()} 属性
     */
    String name() default "";

    /**
     * 操作分类
     * <p>
     * 实际并不是数组，因为枚举不能设置 null 作为默认值
     */
    OperateTypeEnum[] type() default {};

    // ========== 开关字段 ==========

    /**
     * 是否记录操作日志
     */
    boolean enable() default true;

    /**
     * 是否记录方法参数
     */
    boolean logArgs() default true;

    /**
     * 是否记录方法结果的数据
     */
    boolean logResultData() default true;

}
