package com.cheng.log.service.impl;

import cn.hutool.json.JSONUtil;
import com.cheng.log.dto.OperationLogDTO;
import com.cheng.log.service.OperationLogService;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class DefaultOperationLogServiceImpl implements OperationLogService {
    @Override
    public void logHandler(OperationLogDTO operationLogDTO) {
        System.out.println("DefaultOperationLogService" + JSONUtil.toJsonStr(operationLogDTO));
        log.error("DefaultOperationLogService:{}", operationLogDTO);
    }
}
